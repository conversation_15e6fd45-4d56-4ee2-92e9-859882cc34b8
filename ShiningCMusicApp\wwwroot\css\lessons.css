/* Lessons page specific styles */

/* Custom footer styling */
.custom-editor-footer {
    display: flex;
    justify-content: flex-end;
}

/* Mobile lesson card styling */
.clickable-card {
    transition: all 0.2s ease-in-out;
    border: 1px solid #dee2e6;
}

    .clickable-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        border-color: #0066cc;
    }

    .clickable-card:active {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

.lesson-card {
    border-radius: 8px;
}

    .lesson-card .card-body {
        position: relative;
    }

/* Ensure editor dialog appears above mobile list view */
.e-schedule-dialog {
    z-index: 1049 !important;
}

    .e-schedule-dialog .e-dlg-overlay {
        z-index: 1048 !important;
    }

/* Style the desktop QuickInfo popup edit and delete buttons to match mobile buttons */
.e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-edit,
.e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-delete {
    width: 80px !important;
    min-width: 80px !important;
    padding: 8px 16px !important;
    border-radius: 4px !important;
    font-size: 14px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 4px !important;
    transition: background-color 0.2s !important;
    border: none !important;
    cursor: pointer !important;
    color: white !important;
}

/* Edit button styling */
.e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-edit {
    background: #0066cc !important;
}

    .e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-edit:hover {
        background: #0052a3 !important;
    }

    /* Add pencil icon to edit button */
    .e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-edit::before {
        content: "\F4CB" !important; /* Bootstrap pencil icon */
        font-family: "bootstrap-icons" !important;
        margin-right: 4px !important;
        color: white !important;
        font-size: 14px !important;
    }

/* Delete button styling */
.e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-delete {
    background: #dc3545 !important;
}

    .e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-delete:hover {
        background: #c82333 !important;
    }

    /* Add trash icon to delete button */
    .e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-delete::before {
        content: "\F5DE" !important; /* Bootstrap trash icon */
        font-family: "bootstrap-icons" !important;
        margin-right: 4px !important;
        color: white !important;
        font-size: 14px !important;
    }

    /* Ensure icons are white in QuickInfo buttons */
    .e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-edit .e-btn-icon,
    .e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-delete .e-btn-icon,
    .e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-edit .e-icons,
    .e-quick-popup-wrapper .e-event-popup .e-popup-footer .e-event-delete .e-icons {
        color: white !important;
    }

/* Style the button container */
.e-quick-popup-wrapper .e-event-popup .e-popup-footer {
    display: flex !important;
    gap: 8px !important;
    justify-content: flex-end !important;
    padding: 16px !important;
    border-top: 1px solid #eee !important;
}



/* Mobile-specific fixes for Schedule dialog */
@media (max-width: 991.98px) {
    /* Fix z-index to appear above sticky header and mobile list view */
    .e-schedule-dialog.e-dialog,
    .e-schedule-dialog {
        z-index: 1050 !important;
    }

        /* Fix dialog positioning and height for mobile */
        .e-schedule-dialog .e-dlg-container {
            max-height: calc(100vh - 80px) !important;
            margin-top: 40px !important;
            margin-bottom: 40px !important;
        }

        /* Make dialog content scrollable */
        .e-schedule-dialog .e-dlg-content {
            max-height: calc(100vh - 160px) !important;
            overflow-y: auto !important;
            padding: 15px !important;
        }

    /* Ensure custom editor is scrollable */
    .custom-event-editor {
        max-height: calc(100vh - 200px) !important;
        overflow-y: auto !important;
        padding: 15px !important;
    }

    /* Force QuickInfo popup to not be full screen on mobile */
    .e-quick-popup-wrapper {
        position: fixed !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        width: 90% !important;
        max-width: 400px !important;
        height: auto !important;
        max-height: 80vh !important;
        z-index: 1047 !important;
        bottom: auto !important;
    }

    .e-quick-popup-wrapper .e-event-popup {
        position: relative !important;
        width: 100% !important;
        height: auto !important;
        max-height: none !important;
        transform: none !important;
        top: auto !important;
        left: auto !important;
        right: auto !important;
        bottom: auto !important;
        border-radius: 8px !important;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
    }

    /* Force the popup to fit content exactly */
    .e-quick-popup-wrapper .e-event-popup .e-popup-content {
        height: auto !important;
        min-height: auto !important;
        max-height: none !important;
        overflow: visible !important;
        padding: 16px !important;
    }

    /* Remove any fixed heights that might cause extra space */
    .e-quick-popup-wrapper .e-event-popup .e-popup-content .quick-info {
        height: auto !important;
        min-height: auto !important;
    }

    /* Hide the footer area completely on mobile for all users */
    .e-quick-popup-wrapper .e-event-popup .e-popup-footer {
        display: none !important;
    }

    /* Compact form spacing on mobile */
    .custom-event-editor .row {
        margin-bottom: 0.75rem;
    }

    .custom-event-editor .mb-3 {
        margin-bottom: 0.75rem !important;
    }

    /* Custom footer mobile adjustments */
    .custom-editor-footer {
        padding: 10px 15px;
        gap: 8px;
    }
}

/* Extra small mobile adjustments */
@media (max-width: 575.98px) {
    .e-schedule-dialog .e-dlg-container {
        margin: 10px !important;
        max-height: calc(100vh - 20px) !important;
    }

    .e-schedule-dialog .e-dlg-content {
        max-height: calc(100vh - 100px) !important;
        padding: 10px !important;
    }

    .custom-event-editor {
        max-height: calc(100vh - 120px) !important;
        padding: 10px !important;
    }

        /* Stack form fields vertically on very small screens */
        .custom-event-editor .col-md-6 {
            margin-bottom: 0.5rem;
        }

    /* Custom footer extra small mobile adjustments */
    .custom-editor-footer {
        padding: 8px 10px;
        gap: 6px;
    }

        .custom-editor-footer .btn {
            font-size: 0.875rem;
            padding: 0.5rem 1rem;
        }
}
