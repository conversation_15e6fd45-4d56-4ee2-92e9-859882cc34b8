﻿@inherits LayoutComponentBase
@using ShiningCMusicApp.Services
@using ShiningCMusicCommon.Enums
@inject CustomAuthenticationStateProvider AuthStateProvider
@inject NavigationManager Navigation

<div class="page">
    <!-- Simple Mobile Menu - Hidden on desktop -->
    <div class="mobile-menu d-lg-none" id="mobileMenu">
        <div class="mobile-menu-header">
            <h5><i class="fas fa-music me-2"></i>Shining C Music</h5>
            <button type="button" class="mobile-menu-close" onclick="closeMobileMenu()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="mobile-menu-body">
            <AuthorizeView Roles="@UserRoleEnum.Administrator.ToString()">
                <Authorized>
                    <a href="/" onclick="closeMobileMenu()">
                        <i class="bi bi-house-door-fill me-2"></i> Home
                    </a>
                    <a href="/lessons" onclick="closeMobileMenu()">
                        <i class="bi bi-calendar-event-fill me-2"></i> Lessons
                    </a>
                    <a href="/tutors" onclick="closeMobileMenu()">
                        <i class="bi bi-person-fill me-2"></i> Tutors
                    </a>
                    <a href="/students" onclick="closeMobileMenu()">
                        <i class="bi bi-people-fill me-2"></i> Students
                    </a>
                    <a href="/admin" onclick="closeMobileMenu()">
                        <i class="bi bi-gear-fill me-2"></i> Admin
                    </a>
                </Authorized>
            </AuthorizeView>
            <AuthorizeView Roles="@($"{UserRoleEnum.Tutor},{UserRoleEnum.Student}")">
                <Authorized>
                    <a href="/lessons" onclick="closeMobileMenu()">
                        <i class="bi bi-calendar-event-fill me-2"></i> My Lessons
                    </a>
                </Authorized>
            </AuthorizeView>
        </div>
    </div>

    <!-- Mobile Menu Overlay - Hidden on desktop -->
    <div class="mobile-menu-overlay d-lg-none" id="mobileMenuOverlay" onclick="closeMobileMenu()"></div>

    <!-- Desktop sidebar -->
    <div class="sidebar d-none d-lg-block">
        <NavMenu />
    </div>

    <main>
        <!-- Mobile: Hamburger menu button -->
        <button class="mobile-menu-btn d-lg-none" type="button" onclick="openMobileMenu()">
            <span class="hamburger-line"></span>
            <span class="hamburger-line"></span>
            <span class="hamburger-line"></span>
        </button>

        <div class="top-row px-4 d-flex justify-content-end align-items-center">
            <AuthorizeView>
                <Authorized>
                    <div class="d-flex align-items-center">
                        <span class="text-dark me-3">Welcome, @context.User.Identity?.Name</span>
                        <button class="btn btn-outline-primary btn-sm" @onclick="Logout">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </button>
                    </div>
                </Authorized>
            </AuthorizeView>
        </div>

        <article class="content px-4">
            @Body
        </article>
    </main>
</div>

@code {
    private async Task Logout()
    {
        await AuthStateProvider.LogoutAsync();
        Navigation.NavigateTo("/login");
    }

    private string GetShortName(string? fullName)
    {
        if (string.IsNullOrEmpty(fullName))
            return "";

        var parts = fullName.Split(' ');
        if (parts.Length > 1)
            return $"{parts[0][0]}.{parts[^1]}"; // First initial + last name

        return fullName.Length > 10 ? fullName[..10] + "..." : fullName;
    }
}
