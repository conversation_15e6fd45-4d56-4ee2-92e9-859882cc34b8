using Dapper;
using System.Data.SqlClient;
using ShiningCMusicCommon.Models;
using ShiningCMusicApi.Services.Interfaces;

namespace ShiningCMusicApi.Services.Implementations
{
    public class LessonService : ILessonService
    {
        private readonly string _connectionString;

        public LessonService(IConfiguration configuration)
        {
            _connectionString = Environment.GetEnvironmentVariable("ConnectionStrings_MusicSchool")
                ?? configuration.GetConnectionString("MusicSchool")
                ?? throw new InvalidOperationException("Database connection string is missing.");
        }

        public async Task<IEnumerable<ScheduleEvent>> GetLessonsAsync()
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                SELECT
                    l.LessonId as Id,
                    s.StudentName as Subject,
                    l.StartTime,
                    l.EndTime,
                    l.Description,
                    loc.Location as Location,
                    l.RecurrenceRule,
                    l.TutorId,
                    l.StudentId,
                    l.SubjectId,
                    l.LocationId,
                    t.<PERSON>,
                    s.StudentName,
                    subj.Subject as SubjectName,
                    loc.Location as LocationName
                FROM Lessons l
                LEFT JOIN Tutors t ON l.TutorId = t.TutorId
                LEFT JOIN Students s ON l.StudentId = s.StudentId
                LEFT JOIN Subjects subj ON l.SubjectId = subj.SubjectId
                LEFT JOIN Locations loc ON l.LocationId = loc.LocationId
                WHERE l.IsArchived = 0";

            return await connection.QueryAsync<ScheduleEvent>(sql);
        }

        public async Task<ScheduleEvent?> GetLessonAsync(int id)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                SELECT
                    l.LessonId as Id,
                    s.StudentName as Subject,
                    l.StartTime,
                    l.EndTime,
                    l.Description,
                    loc.Location as Location,
                    l.RecurrenceRule,
                    l.TutorId,
                    l.StudentId,
                    l.SubjectId,
                    l.LocationId,
                    t.TutorName,
                    s.StudentName,
                    subj.Subject as SubjectName,
                    loc.Location as LocationName
                FROM Lessons l
                LEFT JOIN Tutors t ON l.TutorId = t.TutorId
                LEFT JOIN Students s ON l.StudentId = s.StudentId
                LEFT JOIN Subjects subj ON l.SubjectId = subj.SubjectId
                LEFT JOIN Locations loc ON l.LocationId = loc.LocationId
                WHERE l.LessonId = @Id AND l.IsArchived = 0";

            return await connection.QueryFirstOrDefaultAsync<ScheduleEvent>(sql, new { Id = id });
        }

        public async Task<ScheduleEvent> CreateLessonAsync(ScheduleEvent lesson)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                INSERT INTO Lessons (SubjectId, Description, StartTime, EndTime, TutorId, StudentId, LocationId, RecurrenceRule, IsRecurring, CreatedUTC)
                VALUES (@SubjectId, @Description, @StartTime, @EndTime, @TutorId, @StudentId, @LocationId, @RecurrenceRule, @IsRecurring, GETUTCDATE());

                SELECT SCOPE_IDENTITY();";

            var isRecurring = !string.IsNullOrEmpty(lesson.RecurrenceRule);

            var newId = await connection.QuerySingleAsync<int>(sql, new
            {
                lesson.SubjectId,
                lesson.Description,
                lesson.StartTime,
                lesson.EndTime,
                lesson.TutorId,
                lesson.StudentId,
                lesson.LocationId,
                lesson.RecurrenceRule,
                IsRecurring = isRecurring
            });

            lesson.Id = newId;
            return lesson;
        }

        public async Task<bool> UpdateLessonAsync(int id, ScheduleEvent lesson)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                UPDATE Lessons
                SET SubjectId = @SubjectId,
                    Description = @Description,
                    StartTime = @StartTime,
                    EndTime = @EndTime,
                    TutorId = @TutorId,
                    StudentId = @StudentId,
                    LocationId = @LocationId,
                    RecurrenceRule = @RecurrenceRule,
                    IsRecurring = @IsRecurring,
                    UpdatedUTC = GETUTCDATE()
                WHERE LessonId = @Id AND IsArchived = 0";

            var isRecurring = !string.IsNullOrEmpty(lesson.RecurrenceRule);

            var rowsAffected = await connection.ExecuteAsync(sql, new
            {
                Id = id,
                lesson.SubjectId,
                lesson.Description,
                lesson.StartTime,
                lesson.EndTime,
                lesson.TutorId,
                lesson.StudentId,
                lesson.LocationId,
                lesson.RecurrenceRule,
                IsRecurring = isRecurring
            });

            return rowsAffected > 0;
        }

        public async Task<bool> DeleteLessonAsync(int id)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                UPDATE Lessons
                SET IsArchived = 1, UpdatedUTC = GETUTCDATE()
                WHERE LessonId = @Id";

            var rowsAffected = await connection.ExecuteAsync(sql, new { Id = id });
            return rowsAffected > 0;
        }

        public async Task<int> PermanentlyDeleteAllLessonsAsync(int olderThanDays)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                DELETE FROM Lessons
                WHERE EndTime < DATEADD(day, -@OlderThanDays, GETUTCDATE())";

            var rowsAffected = await connection.ExecuteAsync(sql, new { OlderThanDays = olderThanDays });
            return rowsAffected;
        }

        public async Task<int> GetAllLessonsCountAsync(int olderThanDays)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                SELECT COUNT(*)
                FROM Lessons
                WHERE EndTime < DATEADD(day, -@OlderThanDays, GETUTCDATE())";

            var count = await connection.QuerySingleAsync<int>(sql, new { OlderThanDays = olderThanDays });
            return count;
        }
    }
}
